<?php

namespace App\Services\Api\Internal\Workspace\CabinetsService\Handlers;

use App\Contracts\HasInsertArrayDtoContract;
use App\Contracts\Repositories\CabinetsRepositoryContract;
use App\Events\CabinetCreated;
use App\Models\User;
use App\Services\Api\Internal\Workspace\CabinetsService\DTO\CabinetDTO;
use App\Traits\HasOrderedUuid;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Event;
use InvalidArgumentException;

class CabinetCreateHandler
{
    use HasOrderedUuid;

    private string $resourceId;

    public function __construct(
        private readonly CabinetsRepositoryContract $cabinetsRepository
    ) {
        $this->resourceId = $this->generateUuid();
    }

    public function run(HasInsertArrayDtoContract $dto): string
    {
        if (!$dto instanceof CabinetDto) {
            throw new InvalidArgumentException();
        }

        $this->cabinetsRepository->insert(
            $dto->toInsertArray($this->resourceId)
        );


        $user = User::where('id', $dto->userId)->first();

        Event::dispatch(new CabinetCreated($this->resourceId, $user));

        $authUser = Auth::user();

        Cache::delete("permissions_{$authUser->id}");
        Cache::delete("employees_{$authUser->id}");
        return $this->resourceId;
    }
}
