<?php

namespace App\Listeners;

use App\Contracts\Repositories\CabinetCurrenciesRepositoryContract;
use App\Contracts\Repositories\CabinetEmployeeRepositoryContract;
use App\Contracts\Repositories\CabinetSettingRepositoryContract;
use App\Contracts\Repositories\DepartmentsRepositoryContract;
use App\Contracts\Repositories\EmployeeRepositoryContract;
use App\Contracts\Repositories\GlobalCurrenciesRepositoryContract;
use App\Contracts\Repositories\ProfitTaxRatesRepositoryContract;
use App\Contracts\Repositories\SaleChannelTypesRepositoryContract;
use App\Contracts\Repositories\SalesChannelsRepositoryContract;
use App\Contracts\Repositories\VatRatesRepositoryContract;
use App\Contracts\Repositories\WarehousesRepositoryContract;
use App\Enums\Api\Internal\NumberingType;
use App\Events\CabinetCreated;
use App\Traits\HasOrderedUuid;
use Illuminate\Contracts\Queue\ShouldQueue;

class CreateCabinetSettings implements ShouldQueue
{
    use HasOrderedUuid;

    public function __construct(
        private readonly CabinetSettingRepositoryContract $repostory,
        private readonly EmployeeRepositoryContract $employeesRepository,
        private readonly CabinetEmployeeRepositoryContract $cabinetEmployeeRepository,
        private readonly DepartmentsRepositoryContract $departmentsRepository,
        private readonly VatRatesRepositoryContract $vatRatesRepository,
        private readonly ProfitTaxRatesRepositoryContract $taxRatesRepository,
        private readonly GlobalCurrenciesRepositoryContract $currenciesRepository,
        private readonly CabinetCurrenciesRepositoryContract $cabinetCurrenciesRepository,
        private readonly SalesChannelsRepositoryContract $salesChannelsRepository,
        private readonly SaleChannelTypesRepositoryContract $salesChannelTypesRepository,
        private readonly WarehousesRepositoryContract $warehouseRepository
    ) {
    }

    public function handle(CabinetCreated $event): void
    {
        $id = $event->cabinetId;
        $user = $event->user;

        $this->repostory->insert([
            'cabinet_id' => $id,
            'numbering_type' => NumberingType::CPNumbers
            ]);

        $employeeId = $this->generateUuid();
        $departmentId = $this->generateUuid();

        $this->departmentsRepository->insert([
            'id' => $departmentId,
            'name' => 'Общий',
            'cabinet_id' => $id,
            'is_default' => true,
        ]);
        $this->employeesRepository->insert([
            'id' => $employeeId,
            'user_id' => $user->id,
            'lastname' => $user->lastname,
            'firstname' => $user->firstname,
            'patronymic' => $user->patronymic,
            'email' => $user->email,
            'department_id' => $departmentId
        ]);
        $this->cabinetEmployeeRepository->insert([
            'cabinet_id' => $id,
            'employee_id' => $employeeId,
        ]);

        $salesChannelId = $this->generateUuid();
        $salesChannelTypes = $this->salesChannelTypesRepository->get(
            ['id', 'name']
        );
        $this->salesChannelsRepository->insert(
            [
                'id' => $salesChannelId,
                'cabinet_id' => $id,
                'name' => 'Общий',
                'is_default' => true,
                'employee_id' => $employeeId,
                'department_id' => $departmentId,
                'sales_channel_type_id' => $salesChannelTypes->where('name', 'Розничные продажи')->first()->id
            ]
        );

        $this->departmentsRepository->update($departmentId, ['sales_channel_id' => $salesChannelId]);

        $vatRates = [0, 5, 7, 10, 20];
        $taxRates = [0, 1, 5, 20, 25];

        $insertVatRates = [];
        $insertTaxRates = [];
        foreach ($vatRates as $rate) {
            $insertVatRates[] = [
                'id' => $this->generateUuid(),
                'cabinet_id' => $id,
                'employee_id' => $employeeId,
                'department_id' => $departmentId,
                'rate' => $rate,
                'description' => $rate == 0 ? 'Без НДС' : "НДС {$rate}%",
                'is_default' => true
            ];
        }
        foreach ($taxRates as $taxRate) {
            $insertTaxRates [] = [
                'id' => $this->generateUuid(),
                'cabinet_id' => $id,
                'employee_id' => $employeeId,
                'department_id' => $departmentId,
                'rate' => $taxRate,
                'description' => $taxRate == 0 ? 'Без налога' : "Налог {$taxRate}%",
                'is_default' => true
            ];
        }

        $rub = $this->currenciesRepository->getByExternalId('R00000');
        $this->cabinetCurrenciesRepository->insert(
            [
                'id' => $this->generateUuid(),
                'cabinet_id' => $id,
                'is_accouting' => true,
                'currency_id' => $rub->id,
                'external_id' => 'R00000',
                'employee_id' => $employeeId,
                'department_id' => $departmentId
            ]
        );

        $this->vatRatesRepository->insert($insertVatRates);
        $this->taxRatesRepository->insert($insertTaxRates);
        $this->warehouseRepository->insert([
            'id' => $this->generateUuid(),
            'cabinet_id' => $id,
            'name' => 'Основной склад',
            'employee_id' => $employeeId,
            'department_id' => $departmentId,
            'is_default' => true,
            'is_common' => true
        ]);
    }
}
